# Province Mapping Scripts

## <PERSON><PERSON> tả
Bộ script Python để cập nhật mapping giữa tỉnh cũ và tỉnh mới trong bảng `___province` theo file CSV sáp nhập hành chính.

## Kết quả đạt được
- ✅ **63/63 tỉnh cũ** đã được cập nhật `new_pti_id`
- ✅ **34 tỉnh mới** đã được liên kết đúng với tỉnh cũ
- ✅ **100% mapping** theo file CSV `tinhthanh-sapnhap.csv`

## Cấu trúc file

### 1. `tinhthanh-sapnhap.csv`
File dữ liệu mapping tỉnh cũ → tỉnh mới:
- `province`: Tên tỉnh mới sau sáp nhập
- `merge_from_province`: Danh sách tỉnh cũ được sáp nhập (phân cách bằng " + ")

### 2. `check_province_data.py`
Script kiểm tra dữ liệu trước khi cập nhật:
- Ki<PERSON>m tra cấu trúc bảng `___province`
- Phân tích dữ liệu tỉnh cũ (is_new=1) và tỉnh mới (is_new=2)
- So sánh dữ liệu CSV với database
- Hiển thị mẫu mapping sẽ được thực hiện

### 3. `update_province_mapping.py`
Script chính để cập nhật mapping:
- Đọc file CSV và phân tích mapping
- Tìm tỉnh mới tương ứng trong database
- Cập nhật `new_pti_id` cho tỉnh cũ
- Sử dụng transaction để đảm bảo an toàn

### 4. `find_missing_provinces.py`
Script tìm kiếm tỉnh thiếu:
- Tìm tỉnh có tên tương tự bằng thuật toán similarity
- Tìm kiếm theo từ khóa
- Hiển thị tất cả tỉnh cũ và trạng thái mapping

### 5. `update_missing_mappings.py`
Script cập nhật 2 mapping cuối cùng:
- Cập nhật "Hoà Bình" → Phú Thọ (pti_id: 25)
- Cập nhật "Thừa Thiên Huế" → TP. Huế (pti_id: 46)

## Cách sử dụng

### Yêu cầu hệ thống
```bash
pip install mysql-connector-python tabulate
```

### Cấu hình database
Cập nhật thông tin kết nối trong các script:
```python
connection = mysql.connector.connect(
    host='127.0.0.1',
    port=3306,
    database='urbox',
    user='root',
    password='root'
)
```

### Quy trình thực hiện
1. **Kiểm tra dữ liệu:**
   ```bash
   python check_province_data.py
   ```

2. **Cập nhật mapping chính:**
   ```bash
   python update_province_mapping.py
   ```

3. **Tìm tỉnh thiếu (nếu cần):**
   ```bash
   python find_missing_provinces.py
   ```

4. **Cập nhật mapping thiếu:**
   ```bash
   python update_missing_mappings.py
   ```

## Kết quả mapping

### Một số ví dụ mapping đã thực hiện:
- **Tuyên Quang mới** (pti_id: 8) ← Hà Giang + Tuyên Quang cũ
- **Phú Thọ mới** (pti_id: 25) ← Hòa Bình + Vĩnh Phúc + Phú Thọ cũ
- **Ninh Bình mới** (pti_id: 37) ← Hà Nam + Ninh Bình + Nam Định cũ
- **TP. Hồ Chí Minh mới** (pti_id: 79) ← Bình Dương + Hồ Chí Minh + Bà Rịa - Vũng Tàu cũ

### Trường hợp đặc biệt đã xử lý:
- **"Hòa Bình" vs "Hoà Bình"** - Khác dấu thanh
- **"TP. Huế" vs "Thừa Thiên Huế"** - Tên viết tắt vs tên đầy đủ

## Thống kê cuối cùng
- Tổng số tỉnh cũ: 63
- Đã mapping: 63 (100%)
- Chưa mapping: 0
- Tỉnh mới: 34

## Lưu ý
- Tất cả script sử dụng transaction để đảm bảo rollback nếu có lỗi
- Dữ liệu được backup tự động trước khi cập nhật
- Script có validation và báo cáo chi tiết

## Tác giả
- Được tạo bởi Augment Agent
- Ngày hoàn thành: 2025-01-17
- Dự án: Cập nhật hệ thống hành chính Việt Nam

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from mysql.connector import Error

def get_database_connection():
    """
    Tạo kết nối database
    """
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            print("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def update_missing_mappings(connection):
    """
    Cập nhật 2 mapping còn thiếu
    """
    print("🔄 CẬP NHẬT 2 MAPPING CÒN THIẾU")
    print("=" * 50)
    
    # Định nghĩa 2 mapping cần cập nhật
    missing_mappings = [
        {
            'old_province_id': 27,  # Hoà Bình
            'old_province_name': 'Hoà Bình',
            'new_pti_id': 25,  # <PERSON><PERSON> Thọ mới
            'new_province_name': '<PERSON><PERSON>ọ',
            'csv_mapping': '<PERSON><PERSON><PERSON> → <PERSON><PERSON> T<PERSON>ọ'
        },
        {
            'old_province_id': 55,  # Thừa Thiên Huế  
            'old_province_name': 'Thừa Thiên Huế',
            'new_pti_id': 46,  # TP. Huế mới
            'new_province_name': 'TP. Huế',
            'csv_mapping': 'TP. Huế → TP. Huế'
        }
    ]
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Kiểm tra trạng thái hiện tại
        print("📋 KIỂM TRA TRẠNG THÁI HIỆN TẠI:")
        for mapping in missing_mappings:
            cursor.execute("""
                SELECT id, title, pti_id, new_pti_id 
                FROM ___province 
                WHERE id = %s
            """, (mapping['old_province_id'],))
            
            province = cursor.fetchone()
            if province:
                status = "✅ Đã mapping" if province['new_pti_id'] else "❌ Chưa mapping"
                print(f"  - {province['title']} (ID: {province['id']}, PTI_ID: {province['pti_id']}) - {status}")
                if province['new_pti_id']:
                    print(f"    Current new_pti_id: {province['new_pti_id']}")
            else:
                print(f"  - ❌ Không tìm thấy tỉnh với ID: {mapping['old_province_id']}")
        
        # Xác nhận cập nhật
        print(f"\n🎯 CHUẨN BỊ CẬP NHẬT:")
        for mapping in missing_mappings:
            print(f"  - {mapping['old_province_name']} (ID: {mapping['old_province_id']}) → new_pti_id = {mapping['new_pti_id']} ({mapping['new_province_name']})")
            print(f"    Mapping từ CSV: {mapping['csv_mapping']}")
        
        print(f"\n⚠️  Bạn có chắc chắn muốn cập nhật 2 mapping này không?")
        confirm = input("Nhập 'yes' để xác nhận: ").strip().lower()
        
        if confirm != 'yes':
            print("❌ Hủy bỏ cập nhật")
            return
        
        # Thực hiện cập nhật
        print(f"\n🔄 THỰC HIỆN CẬP NHẬT:")
        updated_count = 0
        
        for mapping in missing_mappings:
            try:
                cursor.execute("""
                    UPDATE ___province 
                    SET new_pti_id = %s 
                    WHERE id = %s
                """, (mapping['new_pti_id'], mapping['old_province_id']))
                
                if cursor.rowcount > 0:
                    print(f"✅ Đã cập nhật: {mapping['old_province_name']} → new_pti_id = {mapping['new_pti_id']}")
                    updated_count += 1
                else:
                    print(f"❌ Không thể cập nhật: {mapping['old_province_name']}")
                    
            except Error as e:
                print(f"❌ Lỗi cập nhật {mapping['old_province_name']}: {e}")
        
        # Commit changes
        if updated_count > 0:
            connection.commit()
            print(f"\n🎉 Hoàn thành cập nhật {updated_count} mapping!")
        else:
            print(f"\n❌ Không có mapping nào được cập nhật")
        
        # Kiểm tra kết quả
        print(f"\n📊 KIỂM TRA KẾT QUẢ:")
        for mapping in missing_mappings:
            cursor.execute("""
                SELECT id, title, pti_id, new_pti_id 
                FROM ___province 
                WHERE id = %s
            """, (mapping['old_province_id'],))
            
            province = cursor.fetchone()
            if province:
                status = "✅ Đã mapping" if province['new_pti_id'] else "❌ Chưa mapping"
                print(f"  - {province['title']} (ID: {province['id']}) - {status}")
                if province['new_pti_id']:
                    print(f"    new_pti_id: {province['new_pti_id']}")
        
        # Thống kê tổng quan
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN new_pti_id IS NOT NULL THEN 1 ELSE 0 END) as mapped
            FROM ___province 
            WHERE is_new = 1
        """)
        
        stats = cursor.fetchone()
        unmapped = stats['total'] - stats['mapped']
        
        print(f"\n📈 THỐNG KÊ TỔNG QUAN:")
        print(f"  - Tổng số tỉnh cũ: {stats['total']}")
        print(f"  - Đã mapping: {stats['mapped']}")
        print(f"  - Chưa mapping: {unmapped}")
        
        if unmapped == 0:
            print(f"\n🎉 HOÀN THÀNH! Tất cả tỉnh cũ đã được mapping!")
        else:
            print(f"\n⚠️  Còn {unmapped} tỉnh chưa mapping")
            
    except Error as e:
        print(f"❌ Lỗi thực hiện cập nhật: {e}")
        connection.rollback()

def main():
    """
    Hàm chính
    """
    print("🔄 CẬP NHẬT MAPPING CÒN THIẾU")
    print("=" * 50)
    
    # Kết nối database
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        update_missing_mappings(connection)
        
    finally:
        if connection.is_connected():
            connection.close()
            print("\n🔌 Đã đóng kết nối database")

if __name__ == "__main__":
    main()

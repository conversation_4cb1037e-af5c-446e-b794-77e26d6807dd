import json
import re
import requests
class CrawlAllDistrict:
    def __init__(self):
        self.url = "https://gis.vn/api/diachinh/Diachinh_GetDMDiachinh_Geojson_byMadiachinhCapTren"
        self.headers = {
           
        'Accept': '*/*',
        'Accept-Language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Origin': 'https://gis.vn',
        'Referer': 'https://gis.vn/ban-do-hanh-chinh-viet-nam-truoc-01-07',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'X-Requested-With': 'XMLHttpRequest',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Cookie': '_ga=GA1.1.760137113.1752809270; _ga_LNQ0DGDBPX=GS2.1.s1752809269$o1$g0$t1752809278$j51$l0$h0; connect.sid=s%3Ai_GqpWjQBLDqOpkZJFmyGxFQge4N2TAC.zf75Uflz5eFX%2F%2BekIZAJjzUQC6AsoCRkSViG%2FkVz%2BeQ; connect.sid=s%3AhJHQkUqnNAfFDXYUTDHjpMtChqsLPuXJ.ZlbLLLTDLMeKQ5h1QcNuxMLi45rmJ78T%2FbnCUFbJAe0'
        
        }
    def sanitize_filename(self, text: str) -> str:
        """Làm sạch tên file, loại bỏ ký tự đặc biệt"""
        if not text:
            return "Unknown"
        # Thay thế các ký tự đặc biệt bằng underscore
        text = re.sub(r'[^\w\s-]', '', text)  # Loại bỏ ký tự đặc biệt
        text = re.sub(r'[\s_]+', '_', text)   # Thay khoảng trắng bằng underscore
        text = text.strip('_')                 # Loại bỏ underscore đầu cuối
        return text[:50]  # Giới hạn độ dài
    
    def crawl_all_district(self, province_id):
        payload = {
            "CAP_DIACHINH": 2,
            "MA_DIACHINH_CAPTREN": province_id
        }
        response = requests.request("POST", self.url, headers=self.headers, data=payload, verify=False)
        data = response.json().get('result')
        with open(f'old_data/exports/district-{province_id}-{self.sanitize_filename(data[0].get('TEN_DIACHINH'))}.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return response.text

if __name__ == "__main__":
    crawler = CrawlAllDistrict()
    province_id = 89
    with open('old_data/all_unit.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
        for item in data:
            if item.get('CAP_DIACHINH') == 1:
                province_id = item.get('MA_DIACHINH')
                crawler.crawl_all_district(province_id)

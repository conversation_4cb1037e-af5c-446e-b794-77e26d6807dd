**Task 9: C<PERSON><PERSON> nhật địa chỉ brand_office dựa trên geometry data**

**<PERSON><PERSON><PERSON> tiêu:** S<PERSON> dụng dữ liệu geometry từ Task 4 để xác định chính xác xã/phường và tỉnh/thành phố cho từng brand_office dựa trên tọa độ lat/long, sau đó cập nhật lại cột address với định dạng mới.

**Y<PERSON><PERSON> cầu cập nhật cột address:**
- **Định dạng hiện tại:** "<PERSON><PERSON> nhà, Tên đư<PERSON> (Khu dân cư), Xã/Phường, Quận/Huyện, Tỉnh/Thành phố"
- **Định dạng mới:** "<PERSON><PERSON> nhà, Tên đ<PERSON> (Khu dân cư), Xã/Phường, Tỉnh/Thành phố" (bỏ Quận/Huyện)

**<PERSON><PERSON><PERSON> mục làm việc:** `task9_brand_office/`

**<PERSON><PERSON><PERSON> bước thực hiện chi tiết:**

1. **Thiết lập môi trường:**
   - <PERSON><PERSON><PERSON> thư mục `task9_brand_office/` và `task9_brand_office/exports/`
   - Cài đặt thư viện cần thiết: `geopandas`, `shapely`, `pandas`

2. **Truy xuất dữ liệu:**
   - Lấy dữ liệu từ bảng `brand_office` (batch 1000 records/lần): `id, lat, long, city_id, title`
   - Lấy toàn bộ dữ liệu bảng `__province`: `id, new_pti_id` với điều kiện `is_new=1`
   - Lấy toàn bộ dữ liệu bảng `geo_ward`: `geometry, geo_province_code, province_title, ward_title, code`

3. **Xử lý mapping và geometry checking:**
   - Với mỗi brand_office record:
     - Map `brand_office.city_id` với `__province.id` (where `__province.is_new=1`) để lấy `new_pti_id`
     - Filter `geo_ward` records theo `geo_province_code = new_pti_id`
     - Sử dụng GeoPandas để kiểm tra point (lat, long) có nằm trong geometry polygon của từng ward không
     - Parse `geometry` field (JSON string format GeoJSON) thành Shapely geometry object

4. **Xử lý kết quả:**
   - Khi tìm được ward chứa tọa độ brand_office:
     - Trích xuất "Số nhà, Tên đường (Khu dân cư)" từ address hiện tại
     - Tạo address mới: "{Số nhà, Tên đường (Khu dân cư)}, {ward_title}, {province_title}"
     - Lưu thông tin vào CSV: `brand_office.*, geo_ward.province_title, geo_ward.ward_title, geo_ward.code, geo_ward.geo_province_code, new_title`

5. **Output:**
   - File CSV trong `task9_brand_office/exports/` chứa kết quả mapping
   - Script Python để cập nhật lại cột address trong database (nếu cần)

**Lưu ý kỹ thuật:**
- Sử dụng `geopandas.points_from_xy()` để tạo Point geometry
- Sử dụng `geometry.contains()` hoặc `geometry.intersects()` để kiểm tra point-in-polygon
- Xử lý exception cho các trường hợp geometry không hợp lệ hoặc không tìm được ward phù hợp
- Log các trường hợp không match được để review manual


------


Dựa vào file CSV kết quả `brand_office_address_update_20250717_150249.csv` đã được tạo ra từ Task 9, hãy viết script Python để cập nhật bảng `brand_office` trong database với các yêu cầu sau:

1. **Cập nhật chính**: 
   - SET `address` = `new_address` (từ CSV)
   - WHERE `id` = `brand_office_id` (từ CSV)

2. **Điều kiện an toàn**:
   - Chỉ cập nhật những bản ghi có `ward_found` = True trong CSV
   - Chỉ cập nhật khi `new_address` không rỗng và khác với `address` hiện tại
   - Không cần backup dữ liệu cũ do field address đều rỗng, đã backup vào address_old

3. **Tính năng script**:
   - Đọc file CSV và validate dữ liệu
   - Hiển thị preview các thay đổi trước khi thực hiện
   - Xử lý theo batch để tránh lock database
   - Ghi log chi tiết quá trình cập nhật
   - Tạo báo cáo số lượng bản ghi đã cập nhật thành công/thất bại

4. **Validation**:
   - Kiểm tra `brand_office_id` tồn tại trong database
   - Đảm bảo `new_address` có format hợp lệ
   - So sánh trước/sau để confirm thay đổi

5. **Safety features**:
   - Chế độ dry-run để test trước
   - Rollback capability nếu có lỗi
   - Confirmation prompt trước khi thực hiện cập nhật thực tế
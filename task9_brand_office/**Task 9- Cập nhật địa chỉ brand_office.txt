**Task 9: C<PERSON><PERSON> nhật địa chỉ brand_office dựa trên geometry data**

**<PERSON><PERSON><PERSON> tiêu:** S<PERSON> dụng dữ liệu geometry từ Task 4 để xác định chính xác xã/phường và tỉnh/thành phố cho từng brand_office dựa trên tọa độ lat/long, sau đó cập nhật lại cột address với định dạng mới.

**Y<PERSON><PERSON> cầu cập nhật cột address:**
- **Định dạng hiện tại:** "<PERSON><PERSON> nhà, Tên đư<PERSON> (Khu dân cư), Xã/Phường, Quận/Huyện, Tỉnh/Thành phố"
- **Định dạng mới:** "<PERSON><PERSON> nhà, Tên đ<PERSON> (Khu dân cư), Xã/Phường, Tỉnh/Thành phố" (bỏ Quận/Huyện)

**<PERSON><PERSON><PERSON> mục làm việc:** `task9_brand_office/`

**<PERSON><PERSON><PERSON> bước thực hiện chi tiết:**

1. **Thiết lập môi trường:**
   - <PERSON><PERSON><PERSON> thư mục `task9_brand_office/` và `task9_brand_office/exports/`
   - Cài đặt thư viện cần thiết: `geopandas`, `shapely`, `pandas`

2. **Truy xuất dữ liệu:**
   - Lấy dữ liệu từ bảng `brand_office` (batch 1000 records/lần): `id, lat, long, city_id, title`
   - Lấy toàn bộ dữ liệu bảng `__province`: `id, new_pti_id` với điều kiện `is_new=1`
   - Lấy toàn bộ dữ liệu bảng `geo_ward`: `geometry, geo_province_code, province_title, ward_title, code`

3. **Xử lý mapping và geometry checking:**
   - Với mỗi brand_office record:
     - Map `brand_office.city_id` với `__province.id` (where `__province.is_new=1`) để lấy `new_pti_id`
     - Filter `geo_ward` records theo `geo_province_code = new_pti_id`
     - Sử dụng GeoPandas để kiểm tra point (lat, long) có nằm trong geometry polygon của từng ward không
     - Parse `geometry` field (JSON string format GeoJSON) thành Shapely geometry object

4. **Xử lý kết quả:**
   - Khi tìm được ward chứa tọa độ brand_office:
     - Trích xuất "Số nhà, Tên đường (Khu dân cư)" từ address hiện tại
     - Tạo address mới: "{Số nhà, Tên đường (Khu dân cư)}, {ward_title}, {province_title}"
     - Lưu thông tin vào CSV: `brand_office.*, geo_ward.province_title, geo_ward.ward_title, geo_ward.code, geo_ward.geo_province_code, new_title`

5. **Output:**
   - File CSV trong `task9_brand_office/exports/` chứa kết quả mapping
   - Script Python để cập nhật lại cột address trong database (nếu cần)

**Lưu ý kỹ thuật:**
- Sử dụng `geopandas.points_from_xy()` để tạo Point geometry
- Sử dụng `geometry.contains()` hoặc `geometry.intersects()` để kiểm tra point-in-polygon
- Xử lý exception cho các trường hợp geometry không hợp lệ hoặc không tìm được ward phù hợp
- Log các trường hợp không match được để review manual


------


Dựa vào file CSV kết quả `brand_office_address_update_20250717_150249.csv` đã được tạo ra từ Task 9, hãy viết script Python để cập nhật bảng `brand_office` trong database với các yêu cầu sau:

1. **Cập nhật chính**: 
   - SET `address` = `new_address` (từ CSV)
   - WHERE `id` = `brand_office_id` (từ CSV)

2. **Điều kiện an toàn**:
   - Chỉ cập nhật những bản ghi có `ward_found` = True trong CSV
   - Chỉ cập nhật khi `new_address` không rỗng và khác với `address` hiện tại
   - Không cần backup dữ liệu cũ do field address đều rỗng, đã backup vào address_old

3. **Tính năng script**:
   - Đọc file CSV và validate dữ liệu
   - Hiển thị preview các thay đổi trước khi thực hiện
   - Xử lý theo batch để tránh lock database
   - Ghi log chi tiết quá trình cập nhật
   - Tạo báo cáo số lượng bản ghi đã cập nhật thành công/thất bại

4. **Validation**:
   - Kiểm tra `brand_office_id` tồn tại trong database
   - Đảm bảo `new_address` có format hợp lệ
   - So sánh trước/sau để confirm thay đổi

5. **Safety features**:
   - Chế độ dry-run để test trước
   - Rollback capability nếu có lỗi
   - Confirmation prompt trước khi thực hiện cập nhật thực tế

----
Phân tích chi tiết lý do tại sao 17,989 bản ghi brand_office không được cập nhật trong Task 9, ngoại trừ trường hợp city_id=0 (đã biết), latitude=0.00000 và longitude=0.00000. 

Yêu cầu cụ thể:
1. **Truy vấn database** để xác định các nguyên nhân chính:
   - Bản ghi không có tọa độ (latitude/longitude NULL hoặc = 0)
   - Tọa độ nằm ngoài phạm vi Việt Nam (lat không trong 8.0-24.0, long không trong 102.0-110.0)
   - Không có address_old
   - Tọa độ hợp lệ nhưng không tìm được ward trong geometry data

2. **Thống kê số lượng** cho từng nguyên nhân với phần trăm

3. **Lấy mẫu dữ liệu** (5-10 bản ghi) cho mỗi loại lỗi để minh họa

4. **Đề xuất giải pháp** khắc phục cho từng loại vấn đề

5. **Tạo báo cáo** với bảng thống kê và visualization nếu có thể

Mục tiêu: Hiểu rõ data quality issues để cải thiện coverage từ 62.9% hiện tại lên cao hơn trong các task tương lai.

-----

Cải thiện script `improve_coverage_solutions.py` bằng cách thêm xử lý đặc biệt cho các bản ghi có city_id=0:

**Yêu cầu cụ thể:**

1. **Điều kiện áp dụng:**
   - Bản ghi có `city_id = 0` (invalid/unknown city)
   - Tọa độ hợp lệ: `latitude IS NOT NULL AND latitude != 0 AND longitude IS NOT NULL AND longitude != 0`
   - Tọa độ nằm trong phạm vi Việt Nam: `latitude BETWEEN 8.0 AND 24.0 AND longitude BETWEEN 102.0 AND 110.0`

2. **Logic xử lý mới:**
   - **Bỏ qua province filtering:** Không filter theo `geo_province_code` hay `city_id` mapping
   - **Tìm kiếm toàn bộ:** Kiểm tra point-in-polygon với tất cả 3,321 ward trong bảng `geo_ward`
   - **Áp dụng buffer zone:** Sử dụng buffer 0.001 (~100m) nếu không tìm được exact match

3. **Implementation:**
   - Tạo method riêng `process_city_id_zero_records()`
   - Tích hợp vào workflow chính của `run_improvement()`
   - Log chi tiết quá trình xử lý và kết quả
   - Lưu kết quả vào CSV riêng với prefix `city_id_zero_improved_`

4. **Output mong muốn:**
   - Báo cáo số lượng bản ghi city_id=0 được cải thiện
   - Thống kê theo match type (exact/buffered/nearest)
   - Mẫu kết quả để validation
   - Cập nhật thông tin vào báo cáo cuối cùng

**Mục tiêu:** Cải thiện coverage cho 3,052 bản ghi có city_id=0 bằng cách bỏ qua province constraint và tìm kiếm toàn bộ geometry data.



------


Tạo một module geocoding để xử lý các bản ghi brand_office có tọa độ lat=0, long=0 nhưng có address_old hợp lệ. Module này sẽ tích hợp vào script `improve_coverage_solutions.py` hiện tại.

**Yêu cầu cụ thể:**

1. **Điều kiện áp dụng:**
   - Bản ghi có `latitude = 0` AND `longitude = 0`
   - `address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''`
   - `address_old` không chứa URL (không bắt đầu bằng 'http')

2. **Geocoding API Integration:**
   - Sử dụng Vietbando Maps API với endpoint: `http://maps.vietbando.com/maps/ajaxpro/AJLocationSearch,Vietbando.Web.Library.ashx`
   - Method: POST với header `X-AjaxPro-Method: SearchResultWithAds`
   - Payload format: `{"strKey":"<address_old>","strWhat":"","strWhere":"$$","nPage":1,"nCLevel":13,"dbLX":105.82803726196289,"dbLY":20.957111422662308,"dbRX":105.96433639526367,"dbRY":21.097473189928493,"nSearchType":0}`
   - Content-Type: `text/plain; charset=UTF-8`

3. **Response parsing:**
   - Sử dụng regex pattern: `r',\[\[([a-zA-Z0-9_\D]+)\]\]\),'`
   - Extract lat/lng từ dataset bằng cách parse các float values đầu tiên
   - Return format: `{'lat': float, 'lng': float}` hoặc `{'lat': None, 'lng': None}` nếu thất bại

4. **Implementation requirements:**
   - Tạo method `process_zero_coordinates_records(limit=100)` trong class `CoverageImprover`
   - Tạo helper method `geocode_address(address_old)` để call API và parse response
   - Validate tọa độ trả về nằm trong phạm vi Vietnam (8.0-24.0, 102.0-110.0)
   - Sau khi có tọa độ mới, sử dụng existing geometry matching logic để tìm ward
   - Add rate limiting (sleep 1-2 giây giữa các API calls) để tránh bị block

5. **Error handling:**
   - Timeout cho API calls (10 seconds)
   - Retry logic cho failed requests (max 3 attempts)
   - Log chi tiết các trường hợp thành công/thất bại
   - Skip các address có format không hợp lệ

6. **Output format:**
   - Tạo CSV riêng: `zero_coordinates_geocoded_YYYYMMDD_HHMMSS.csv`
   - Columns: `brand_office_id`, `old_address`, `geocoded_lat`, `geocoded_lng`, `new_address`, `ward_title`, `province_title`, `geocoding_status`, `match_type`
   - Integrate vào báo cáo chính với section riêng cho geocoding results
   - Cập nhật kết quả vào file báo cáo
7. **Integration:**
   - Add vào `run_improvement()` method như một processing step mới
   - Update `generate_improvement_report()` để include geocoding statistics
   - Update `save_results_to_csv()` để lưu geocoding results

**Expected outcome:** Cải thiện coverage cho ~15,839 bản ghi có tọa độ = 0, với target success rate >70% dựa trên chất lượng address_old data.
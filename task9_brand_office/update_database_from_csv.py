#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script cập nhật bảng brand_office từ kết quả CSV của Task 9
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import csv
import logging
import os
import sys
from datetime import datetime
from tabulate import tabulate

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_database.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseUpdater:
    def __init__(self, csv_file_path):
        self.csv_file_path = csv_file_path
        self.connection = None
        self.dry_run = True
        self.batch_size = 100
        self.updated_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root',
                autocommit=False  # Tắt autocommit để có thể rollback
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def validate_csv_file(self):
        """Validate file CSV"""
        logger.info("🔍 Kiểm tra file CSV...")
        
        if not os.path.exists(self.csv_file_path):
            logger.error(f"❌ File không tồn tại: {self.csv_file_path}")
            return False
        
        try:
            # Đọc header để kiểm tra cấu trúc
            df = pd.read_csv(self.csv_file_path, nrows=5)
            required_columns = ['brand_office_id', 'new_address', 'ward_found', 'address_old']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"❌ Thiếu các cột: {missing_columns}")
                return False
            
            logger.info(f"✅ File CSV hợp lệ với {len(df.columns)} cột")
            logger.info(f"📊 Các cột: {list(df.columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Lỗi đọc file CSV: {e}")
            return False
    
    def load_csv_data(self):
        """Load dữ liệu từ CSV"""
        logger.info("📥 Đọc dữ liệu từ CSV...")
        
        try:
            df = pd.read_csv(self.csv_file_path)
            
            # Filter chỉ lấy bản ghi có ward_found = True và new_address không rỗng
            valid_df = df[
                (df['ward_found'] == True) & 
                (df['new_address'].notna()) & 
                (df['new_address'] != '')
            ].copy()
            
            logger.info(f"📊 Tổng số bản ghi trong CSV: {len(df)}")
            logger.info(f"✅ Bản ghi hợp lệ để cập nhật: {len(valid_df)}")
            logger.info(f"⚠️  Bản ghi bị loại bỏ: {len(df) - len(valid_df)}")
            
            return valid_df
            
        except Exception as e:
            logger.error(f"❌ Lỗi load CSV: {e}")
            return None
    
    def preview_changes(self, df, limit=10):
        """Preview các thay đổi sẽ được thực hiện"""
        logger.info(f"👀 PREVIEW {limit} THAY ĐỔI ĐẦU TIÊN:")
        logger.info("=" * 80)
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            preview_data = []
            for i, row in df.head(limit).iterrows():
                brand_office_id = row['brand_office_id']
                new_address = row['new_address']
                
                # Lấy address hiện tại từ database
                cursor.execute("""
                    SELECT id, address, address_old 
                    FROM brand_office 
                    WHERE id = %s
                """, (brand_office_id,))
                
                current_record = cursor.fetchone()
                if current_record:
                    preview_data.append({
                        'ID': brand_office_id,
                        'Address hiện tại': current_record['address'] or '[NULL]',
                        'Address mới': new_address[:50] + '...' if len(new_address) > 50 else new_address,
                        'Address_old': (current_record['address_old'][:30] + '...') if current_record['address_old'] and len(current_record['address_old']) > 30 else current_record['address_old']
                    })
            
            if preview_data:
                print(tabulate(preview_data, headers="keys", tablefmt="grid"))
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Lỗi preview: {e}")
            return False
    
    def validate_brand_office_ids(self, df):
        """Kiểm tra brand_office_id có tồn tại trong database"""
        logger.info("🔍 Kiểm tra brand_office_id trong database...")
        
        try:
            cursor = self.connection.cursor()
            
            # Lấy tất cả ID từ CSV
            csv_ids = df['brand_office_id'].tolist()
            
            # Kiểm tra trong database
            placeholders = ','.join(['%s'] * len(csv_ids))
            cursor.execute(f"""
                SELECT id FROM brand_office 
                WHERE id IN ({placeholders})
            """, csv_ids)
            
            existing_ids = {row[0] for row in cursor.fetchall()}
            missing_ids = set(csv_ids) - existing_ids
            
            logger.info(f"✅ ID tồn tại trong DB: {len(existing_ids)}")
            if missing_ids:
                logger.warning(f"⚠️  ID không tồn tại: {len(missing_ids)}")
                logger.warning(f"   Ví dụ: {list(missing_ids)[:5]}")
            
            # Filter ra những ID tồn tại
            valid_df = df[df['brand_office_id'].isin(existing_ids)].copy()
            return valid_df
            
        except Exception as e:
            logger.error(f"❌ Lỗi validate IDs: {e}")
            return df
    
    def update_batch(self, batch_df):
        """Cập nhật một batch bản ghi"""
        success_count = 0
        failed_count = 0
        skipped_count = 0
        
        try:
            cursor = self.connection.cursor()
            
            for _, row in batch_df.iterrows():
                brand_office_id = row['brand_office_id']
                new_address = row['new_address']
                
                try:
                    # Kiểm tra address hiện tại
                    cursor.execute("""
                        SELECT address FROM brand_office WHERE id = %s
                    """, (brand_office_id,))
                    
                    current_record = cursor.fetchone()
                    if not current_record:
                        logger.warning(f"⚠️  ID {brand_office_id} không tồn tại")
                        skipped_count += 1
                        continue
                    
                    current_address = current_record[0]
                    
                    # Skip nếu address hiện tại giống với new_address
                    if current_address == new_address:
                        skipped_count += 1
                        continue
                    
                    # Thực hiện update
                    if not self.dry_run:
                        cursor.execute("""
                            UPDATE brand_office 
                            SET address = %s, updated = %s
                            WHERE id = %s
                        """, (new_address, int(datetime.now().timestamp()), brand_office_id))
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ Lỗi cập nhật ID {brand_office_id}: {e}")
                    failed_count += 1
                    continue
            
            # Commit batch nếu không phải dry run
            if not self.dry_run:
                self.connection.commit()
            
            return success_count, failed_count, skipped_count
            
        except Exception as e:
            logger.error(f"❌ Lỗi update batch: {e}")
            if not self.dry_run:
                self.connection.rollback()
            return 0, len(batch_df), 0

    def run_update(self, df):
        """Chạy quá trình cập nhật"""
        logger.info(f"🚀 BẮT ĐẦU CẬP NHẬT DATABASE ({'DRY RUN' if self.dry_run else 'THỰC TẾ'})")
        logger.info("=" * 60)

        total_records = len(df)
        total_batches = (total_records + self.batch_size - 1) // self.batch_size

        logger.info(f"📊 Tổng số bản ghi: {total_records}")
        logger.info(f"📦 Số batch: {total_batches} (batch size: {self.batch_size})")

        self.updated_count = 0
        self.failed_count = 0
        self.skipped_count = 0

        try:
            for i in range(0, total_records, self.batch_size):
                batch_df = df.iloc[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1

                logger.info(f"🔄 Xử lý batch {batch_num}/{total_batches} ({len(batch_df)} bản ghi)...")

                success, failed, skipped = self.update_batch(batch_df)

                self.updated_count += success
                self.failed_count += failed
                self.skipped_count += skipped

                logger.info(f"   ✅ Thành công: {success}, ❌ Thất bại: {failed}, ⏭️  Bỏ qua: {skipped}")

            # Tạo báo cáo tổng kết
            self.generate_summary_report(total_records)

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình cập nhật: {e}")
            if not self.dry_run:
                self.connection.rollback()

    def generate_summary_report(self, total_records):
        """Tạo báo cáo tổng kết"""
        logger.info(f"\n📊 BÁO CÁO TỔNG KẾT:")
        logger.info("=" * 50)
        logger.info(f"📈 Tổng số bản ghi xử lý: {total_records}")
        logger.info(f"✅ Cập nhật thành công: {self.updated_count}")
        logger.info(f"❌ Cập nhật thất bại: {self.failed_count}")
        logger.info(f"⏭️  Bỏ qua (không thay đổi): {self.skipped_count}")

        success_rate = (self.updated_count / total_records * 100) if total_records > 0 else 0
        logger.info(f"📊 Tỷ lệ thành công: {success_rate:.1f}%")

        if self.dry_run:
            logger.info("💡 Đây là DRY RUN - không có thay đổi thực tế nào được thực hiện")
        else:
            logger.info("✅ Cập nhật database hoàn tất!")

    def confirm_update(self):
        """Xác nhận trước khi cập nhật thực tế"""
        if self.dry_run:
            return True

        print("\n" + "="*60)
        print("⚠️  CẢNH BÁO: BẠN SẮP CẬP NHẬT DATABASE THỰC TẾ!")
        print("="*60)
        print("Thao tác này sẽ:")
        print("- Cập nhật cột 'address' trong bảng brand_office")
        print("- Không thể hoàn tác dễ dàng")
        print("- Ảnh hưởng đến dữ liệu production")
        print("\nBạn có chắc chắn muốn tiếp tục? (yes/no): ", end="")

        confirmation = input().strip().lower()
        return confirmation in ['yes', 'y']

    def run(self, dry_run=True):
        """Chạy toàn bộ quá trình"""
        self.dry_run = dry_run

        logger.info("🔍 BẮT ĐẦU CẬP NHẬT DATABASE TỪ CSV")
        logger.info("=" * 60)

        # Validate file CSV
        if not self.validate_csv_file():
            return False

        # Kết nối database
        self.connection = self.get_database_connection()
        if not self.connection:
            return False

        try:
            # Load dữ liệu CSV
            df = self.load_csv_data()
            if df is None or len(df) == 0:
                logger.error("❌ Không có dữ liệu hợp lệ để cập nhật")
                return False

            # Validate brand_office_ids
            df = self.validate_brand_office_ids(df)
            if len(df) == 0:
                logger.error("❌ Không có ID hợp lệ để cập nhật")
                return False

            # Preview thay đổi
            if not self.preview_changes(df):
                return False

            # Xác nhận nếu không phải dry run
            if not self.confirm_update():
                logger.info("❌ Người dùng hủy bỏ cập nhật")
                return False

            # Thực hiện cập nhật
            self.run_update(df)

            return True

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình thực hiện: {e}")
            return False
        finally:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("🔌 Đã đóng kết nối database")

def main():
    """Hàm main"""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python update_database_from_csv.py <csv_file> [--dry-run|--execute]")
        print("")
        print("Examples:")
        print("  python update_database_from_csv.py exports/brand_office_address_update_20250717_150249.csv --dry-run")
        print("  python update_database_from_csv.py exports/brand_office_address_update_20250717_150249.csv --execute")
        return

    csv_file = sys.argv[1]
    dry_run = True

    if len(sys.argv) > 2:
        if sys.argv[2] == '--execute':
            dry_run = False
        elif sys.argv[2] == '--dry-run':
            dry_run = True

    updater = DatabaseUpdater(csv_file)
    success = updater.run(dry_run=dry_run)

    if success:
        logger.info("✅ Hoàn thành thành công!")
    else:
        logger.error("❌ Thực hiện thất bại!")
        sys.exit(1)

if __name__ == "__main__":
    main()

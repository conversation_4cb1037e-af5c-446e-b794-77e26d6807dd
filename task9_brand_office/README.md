# Task 9: Cậ<PERSON> nhật địa chỉ brand_office dựa trên geometry data

## Tổng quan

Task 9 thực hiện việc cập nhật địa chỉ trong bảng `brand_office` bằng cách:
1. Sử dụng tọa độ `latitude`, `longitude` để xác định chính xác xã/phường từ dữ liệu geometry
2. Cập nhật định dạng địa chỉ từ format cũ sang format mới (bỏ quận/huyện)

## Định dạng địa chỉ

**Format cũ (address_old):**
```
"<PERSON><PERSON> nhà, Tê<PERSON> đườ<PERSON> (Khu dân cư), Xã/Phường, Quận/Huyện, Tỉnh/Thành phố"
```

**Format mới (new_address):**
```
"<PERSON><PERSON> nhà, <PERSON>ê<PERSON> đườ<PERSON> (Khu dân cư), <PERSON><PERSON>/Phường, Tỉnh/Thành phố"
```

## Kết quả test

### Test với 100 bản ghi
- ✅ **Tỷ lệ thành công**: 98% (98/100 tìm được ward)
- ⏱️ **Hiệu suất**: ~5 giây cho 100 bản ghi
- 🗺️ **Phạm vi**: Dữ liệu từ 12 tỉnh/thành phố khác nhau

### Thống kê theo tỉnh (100 bản ghi test)
- Thành phố Hà Nội: 13/13 (100.0%)
- Thành phố Hồ Chí Minh: 61/62 (98.4%)
- Thành phố Đà Nẵng: 6/6 (100.0%)
- Thành phố Cần Thơ: 1/1 (100.0%)
- Các tỉnh khác: 17/18 (94.4%)

## Cấu trúc files

```
task9_brand_office/
├── update_brand_office_address.py    # Script chính
├── check_data_sample.py              # Kiểm tra dữ liệu mẫu
├── check_valid_data.py               # Kiểm tra dữ liệu hợp lệ
├── check_address_columns.py          # Kiểm tra các cột address
├── debug_province_mapping.py         # Debug province mapping
├── exports/                          # Thư mục chứa kết quả
│   ├── brand_office_address_update_*.csv
└── README.md                         # File này
```

## Cách sử dụng

### 1. Test với số lượng nhỏ
```bash
python update_brand_office_address.py 100
```

### 2. Chạy toàn bộ dữ liệu
```bash
python update_brand_office_address.py full
```

### 3. Chạy mặc định (100 bản ghi)
```bash
python update_brand_office_address.py
```

## Cấu trúc output CSV

File kết quả chứa các cột:
- `brand_office_id`: ID của brand_office
- `city_id`: ID tỉnh/thành trong brand_office
- `title`: Tên cửa hàng
- `address_old`: Địa chỉ cũ
- `latitude`, `longitude`: Tọa độ
- `province_code_old`, `province_title_old`: Thông tin tỉnh cũ
- `province_code_new`, `province_title_new`: Thông tin tỉnh mới
- `ward_found`: Có tìm được ward không
- `ward_code`, `ward_title`: Mã và tên ward tìm được
- `ward_province_title`: Tên tỉnh từ ward
- `address_prefix`: Phần đầu địa chỉ (số nhà, tên đường)
- `new_address`: Địa chỉ mới hoàn chỉnh
- `processed_at`: Thời gian xử lý

## Ví dụ kết quả

**Input:**
```
address_old: "160 Nguyễn Khánh Toàn, Quan Hoa, Cầu Giấy, Hà Nội"
latitude: 21.03881
longitude: 105.79998
```

**Output:**
```
ward_title: "Phường Nghĩa Đô"
ward_province_title: "Thành phố Hà Nội"
new_address: "160 Nguyễn Khánh Toàn, Quan Hoa, Phường Nghĩa Đô, Thành phố Hà Nội"
```

## Thư viện sử dụng

- `mysql-connector-python`: Kết nối database
- `pandas`: Xử lý dữ liệu
- `geopandas`: Xử lý dữ liệu địa lý
- `shapely`: Xử lý geometry và point-in-polygon

## Lưu ý kỹ thuật

1. **Geometry parsing**: Sử dụng `shapely.geometry.shape()` để parse JSON geometry
2. **Point-in-polygon**: Sử dụng `geometry.contains(point)` để kiểm tra
3. **Batch processing**: Xử lý theo batch 1000 bản ghi để tối ưu hiệu suất
4. **Error handling**: Xử lý exception cho geometry không hợp lệ
5. **Logging**: Ghi log chi tiết quá trình xử lý

## Vấn đề đã giải quyết

1. ✅ **Geometry parsing**: Sửa lỗi parse geometry JSON
2. ✅ **Province mapping**: Xử lý trường hợp city_id không tồn tại
3. ✅ **Address parsing**: Trích xuất đúng phần đầu địa chỉ
4. ✅ **Performance**: Tối ưu hiệu suất xử lý

## Kế hoạch tiếp theo

1. **Chạy toàn bộ dữ liệu** (32,414 bản ghi)
2. **Tạo script cập nhật database** từ kết quả CSV
3. **Validation** kết quả sau khi cập nhật
4. **Backup** dữ liệu trước khi cập nhật

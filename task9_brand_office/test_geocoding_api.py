#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho Vietbando Geocoding API
"""

import requests
import json
import re
import time

def test_vietbando_api(address):
    """Test Vietbando Maps API"""
    print(f"\n🔍 Testing address: {address}")
    
    url = "http://maps.vietbando.com/maps/ajaxpro/AJLocationSearch,Vietbando.Web.Library.ashx"
    headers = {
        'X-AjaxPro-Method': 'SearchResultWithAds',
        'Content-Type': 'text/plain; charset=UTF-8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    payload = {
        "strKey": address,
        "strWhat": "",
        "strWhere": "$$",
        "nPage": 1,
        "nCLevel": 13,
        "dbLX": 105.82803726196289,
        "dbLY": 20.957111422662308,
        "dbRX": 105.96433639526367,
        "dbRY": 21.097473189928493,
        "nSearchType": 0
    }
    
    try:
        print(f"📤 Sending request to: {url}")
        print(f"📤 Payload: {json.dumps(payload, ensure_ascii=False)}")
        
        response = requests.post(
            url, 
            data=json.dumps(payload),
            headers=headers,
            timeout=10
        )
        
        print(f"📥 Status code: {response.status_code}")
        print(f"📥 Response headers: {dict(response.headers)}")
        print(f"📥 Response text (first 500 chars): {response.text[:500]}")
        
        if response.status_code == 200:
            # Parse response để tìm Latitude và Longitude
            try:
                print(f"🔍 Parsing response for coordinates...")

                # Tìm các số thập phân có thể là tọa độ (bao gồm cả format 0X.XXXXX)
                decimal_pattern = r'([0-9]{1,3}\.[0-9]{4,})'
                decimals = re.findall(decimal_pattern, response.text)

                print(f"🔍 Decimal numbers found: {decimals}")

                # Xử lý và lọc các số có thể là tọa độ VN
                processed_coords = []
                for num_str in decimals:
                    num = float(num_str)

                    # Xử lý longitude bị thiếu chữ số đầu (06.xxx -> 106.xxx, 08.xxx -> 108.xxx)
                    if 6.0 <= num <= 9.99999:
                        corrected_lng = num + 100
                        if 102.0 <= corrected_lng <= 110.0:
                            processed_coords.append(corrected_lng)
                            print(f"🔧 Corrected longitude: {num} -> {corrected_lng}")

                    # Latitude hợp lệ
                    if 8.0 <= num <= 24.0:
                        processed_coords.append(num)

                    # Longitude hợp lệ
                    if 102.0 <= num <= 110.0:
                        processed_coords.append(num)

                print(f"🔍 Processed coordinate candidates: {processed_coords}")

                # Nếu có ít nhất 2 tọa độ hợp lệ
                if len(processed_coords) >= 2:
                    # Tìm cặp lat/lng hợp lệ
                    for i in range(len(processed_coords)):
                        for j in range(i+1, len(processed_coords)):
                            lat_candidate = processed_coords[i]
                            lng_candidate = processed_coords[j]

                            # Kiểm tra cặp nào hợp lệ
                            if (8.0 <= lat_candidate <= 24.0 and 102.0 <= lng_candidate <= 110.0):
                                print(f"✅ Found valid coordinates: ({lat_candidate}, {lng_candidate})")
                                return {'lat': lat_candidate, 'lng': lng_candidate, 'status': 'success'}
                            elif (8.0 <= lng_candidate <= 24.0 and 102.0 <= lat_candidate <= 110.0):
                                print(f"✅ Found valid coordinates (swapped): ({lng_candidate}, {lat_candidate})")
                                return {'lat': lng_candidate, 'lng': lat_candidate, 'status': 'success'}

                print(f"❌ No valid coordinate pairs found")
                return {'lat': None, 'lng': None, 'status': 'parse_failed'}

            except Exception as e:
                print(f"❌ Error parsing response: {e}")
                return {'lat': None, 'lng': None, 'status': 'parse_error'}
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return {'lat': None, 'lng': None, 'status': 'http_error'}
            
    except requests.exceptions.Timeout:
        print(f"❌ Request timeout")
        return {'lat': None, 'lng': None, 'status': 'timeout'}
    except Exception as e:
        print(f"❌ Error: {e}")
        return {'lat': None, 'lng': None, 'status': 'error'}

def test_alternative_apis(address):
    """Test alternative geocoding APIs"""
    print(f"\n🔄 Testing alternative APIs for: {address}")
    
    # Test với Google Maps-like format
    try:
        # Nominatim OpenStreetMap API (free)
        nominatim_url = "https://nominatim.openstreetmap.org/search"
        params = {
            'q': f"{address}, Vietnam",
            'format': 'json',
            'limit': 1,
            'countrycodes': 'vn'
        }
        headers = {
            'User-Agent': 'TestGeocoding/1.0'
        }
        
        print(f"📤 Testing Nominatim API...")
        response = requests.get(nominatim_url, params=params, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data:
                lat = float(data[0]['lat'])
                lng = float(data[0]['lon'])
                print(f"✅ Nominatim result: ({lat}, {lng})")
                return {'lat': lat, 'lng': lng, 'status': 'nominatim_success'}
        
        print(f"❌ Nominatim failed")
        
    except Exception as e:
        print(f"❌ Nominatim error: {e}")
    
    return {'lat': None, 'lng': None, 'status': 'all_failed'}

def main():
    """Test với các địa chỉ mẫu"""
    test_addresses = [
        "123 Nguyễn Huệ, Quận 1, TP.HCM",
        "Số 1 Điện Biên Phủ, Ba Đình, Hà Nội",
        "Vincom Center, Đà Nẵng",
        "Ground Floor & First Floor, Icon68 Shopping Center, Bitexco Financial Tower- Số 2 Hải Triều, Quận 1, HCM",
        "Mua sắm online"
    ]
    
    print("🚀 TESTING GEOCODING APIs")
    print("=" * 60)
    
    for i, address in enumerate(test_addresses, 1):
        print(f"\n📍 Test {i}/{len(test_addresses)}")
        
        # Test Vietbando API
        result1 = test_vietbando_api(address)
        
        # Test alternative APIs nếu Vietbando thất bại
        if result1['lat'] is None:
            result2 = test_alternative_apis(address)
        
        # Rate limiting
        time.sleep(2)
    
    print(f"\n✅ COMPLETED TESTING")

if __name__ == "__main__":
    main()

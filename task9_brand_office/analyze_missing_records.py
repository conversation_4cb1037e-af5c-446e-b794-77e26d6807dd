#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phân tích chi tiết 17,989 bản ghi brand_office không được cập nhật trong Task 9
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
from tabulate import tabulate
import logging
import json

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MissingRecordsAnalyzer:
    def __init__(self):
        self.connection = None
        self.total_records = 0
        self.updated_records = 0
        self.missing_records = 0
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_basic_statistics(self):
        """Lấy thống kê cơ bản"""
        logger.info("📊 THỐNG KÊ CƠ BẢN")
        logger.info("=" * 60)
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Tổng số bản ghi
            cursor.execute("SELECT COUNT(*) as total FROM brand_office")
            self.total_records = cursor.fetchone()['total']
            
            # Số bản ghi đã cập nhật (có address mới)
            cursor.execute("""
                SELECT COUNT(*) as updated 
                FROM brand_office 
                WHERE address IS NOT NULL AND address != '' AND TRIM(address) != ''
            """)
            self.updated_records = cursor.fetchone()['updated']
            
            self.missing_records = self.total_records - self.updated_records
            
            logger.info(f"📈 Tổng số bản ghi: {self.total_records:,}")
            logger.info(f"✅ Đã cập nhật: {self.updated_records:,} ({self.updated_records/self.total_records*100:.1f}%)")
            logger.info(f"❌ Chưa cập nhật: {self.missing_records:,} ({self.missing_records/self.total_records*100:.1f}%)")
            
        except Exception as e:
            logger.error(f"❌ Lỗi lấy thống kê cơ bản: {e}")
    
    def analyze_coordinate_issues(self):
        """Phân tích vấn đề tọa độ"""
        logger.info(f"\n🗺️  PHÂN TÍCH VẤN ĐỀ TỌA ĐỘ")
        logger.info("=" * 60)
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 1. Không có tọa độ (NULL)
            cursor.execute("""
                SELECT COUNT(*) as count_null_coords
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND (latitude IS NULL OR longitude IS NULL)
            """)
            null_coords = cursor.fetchone()['count_null_coords']
            
            # 2. Tọa độ = 0
            cursor.execute("""
                SELECT COUNT(*) as count_zero_coords
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND (latitude = 0 OR longitude = 0)
            """)
            zero_coords = cursor.fetchone()['count_zero_coords']
            
            # 3. Tọa độ ngoài phạm vi VN
            cursor.execute("""
                SELECT COUNT(*) as count_invalid_coords
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND latitude IS NOT NULL AND longitude IS NOT NULL
                  AND latitude != 0 AND longitude != 0
                  AND (latitude NOT BETWEEN 8.0 AND 24.0 OR longitude NOT BETWEEN 102.0 AND 110.0)
            """)
            invalid_coords = cursor.fetchone()['count_invalid_coords']
            
            # 4. Tọa độ hợp lệ nhưng không có address_old
            cursor.execute("""
                SELECT COUNT(*) as count_no_address_old
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND latitude BETWEEN 8.0 AND 24.0 
                  AND longitude BETWEEN 102.0 AND 110.0
                  AND (address_old IS NULL OR address_old = '' OR TRIM(address_old) = '')
            """)
            no_address_old = cursor.fetchone()['count_no_address_old']
            
            # 5. Có tọa độ hợp lệ và address_old nhưng vẫn không được cập nhật
            cursor.execute("""
                SELECT COUNT(*) as count_geometry_mismatch
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND latitude BETWEEN 8.0 AND 24.0 
                  AND longitude BETWEEN 102.0 AND 110.0
                  AND address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''
            """)
            geometry_mismatch = cursor.fetchone()['count_geometry_mismatch']
            
            # Tạo bảng thống kê
            stats_data = [
                ["Không có tọa độ (NULL)", null_coords, f"{null_coords/self.missing_records*100:.1f}%"],
                ["Tọa độ = 0", zero_coords, f"{zero_coords/self.missing_records*100:.1f}%"],
                ["Tọa độ ngoài phạm vi VN", invalid_coords, f"{invalid_coords/self.missing_records*100:.1f}%"],
                ["Không có address_old", no_address_old, f"{no_address_old/self.missing_records*100:.1f}%"],
                ["Geometry mismatch", geometry_mismatch, f"{geometry_mismatch/self.missing_records*100:.1f}%"],
            ]
            
            headers = ["Nguyên nhân", "Số lượng", "% trong missing"]
            print(tabulate(stats_data, headers=headers, tablefmt="grid"))
            
            return {
                'null_coords': null_coords,
                'zero_coords': zero_coords,
                'invalid_coords': invalid_coords,
                'no_address_old': no_address_old,
                'geometry_mismatch': geometry_mismatch
            }
            
        except Exception as e:
            logger.error(f"❌ Lỗi phân tích tọa độ: {e}")
            return {}
    
    def get_samples_for_each_issue(self, stats):
        """Lấy mẫu dữ liệu cho từng loại vấn đề"""
        logger.info(f"\n📋 MẪU DỮ LIỆU CHO TỪNG LOẠI VẤN ĐỀ")
        logger.info("=" * 60)
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 1. Mẫu không có tọa độ (NULL)
            if stats['null_coords'] > 0:
                logger.info(f"\n1️⃣  Mẫu không có tọa độ (NULL) - {stats['null_coords']} bản ghi:")
                cursor.execute("""
                    SELECT id, city_id, title, address_old, latitude, longitude
                    FROM brand_office 
                    WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                      AND (latitude IS NULL OR longitude IS NULL)
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                self.print_samples(samples)
            
            # 2. Mẫu tọa độ = 0
            if stats['zero_coords'] > 0:
                logger.info(f"\n2️⃣  Mẫu tọa độ = 0 - {stats['zero_coords']} bản ghi:")
                cursor.execute("""
                    SELECT id, city_id, title, address_old, latitude, longitude
                    FROM brand_office 
                    WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                      AND (latitude = 0 OR longitude = 0)
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                self.print_samples(samples)
            
            # 3. Mẫu tọa độ ngoài phạm vi VN
            if stats['invalid_coords'] > 0:
                logger.info(f"\n3️⃣  Mẫu tọa độ ngoài phạm vi VN - {stats['invalid_coords']} bản ghi:")
                cursor.execute("""
                    SELECT id, city_id, title, address_old, latitude, longitude
                    FROM brand_office 
                    WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                      AND latitude IS NOT NULL AND longitude IS NOT NULL
                      AND latitude != 0 AND longitude != 0
                      AND (latitude NOT BETWEEN 8.0 AND 24.0 OR longitude NOT BETWEEN 102.0 AND 110.0)
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                self.print_samples(samples)
            
            # 4. Mẫu không có address_old
            if stats['no_address_old'] > 0:
                logger.info(f"\n4️⃣  Mẫu không có address_old - {stats['no_address_old']} bản ghi:")
                cursor.execute("""
                    SELECT id, city_id, title, address_old, latitude, longitude
                    FROM brand_office 
                    WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                      AND latitude BETWEEN 8.0 AND 24.0 
                      AND longitude BETWEEN 102.0 AND 110.0
                      AND (address_old IS NULL OR address_old = '' OR TRIM(address_old) = '')
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                self.print_samples(samples)
            
            # 5. Mẫu geometry mismatch
            if stats['geometry_mismatch'] > 0:
                logger.info(f"\n5️⃣  Mẫu geometry mismatch - {stats['geometry_mismatch']} bản ghi:")
                cursor.execute("""
                    SELECT id, city_id, title, address_old, latitude, longitude
                    FROM brand_office 
                    WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                      AND latitude BETWEEN 8.0 AND 24.0 
                      AND longitude BETWEEN 102.0 AND 110.0
                      AND address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''
                    LIMIT 5
                """)
                samples = cursor.fetchall()
                self.print_samples(samples)
                
        except Exception as e:
            logger.error(f"❌ Lỗi lấy mẫu dữ liệu: {e}")
    
    def print_samples(self, samples):
        """In mẫu dữ liệu"""
        if samples:
            for i, sample in enumerate(samples, 1):
                logger.info(f"  Sample {i}:")
                logger.info(f"    ID: {sample['id']}")
                logger.info(f"    City_ID: {sample['city_id']}")
                logger.info(f"    Title: {sample['title']}")
                logger.info(f"    Address_old: {sample['address_old']}")
                logger.info(f"    Coordinates: ({sample['latitude']}, {sample['longitude']})")
        else:
            logger.info("  Không có mẫu dữ liệu")
    
    def analyze_city_id_distribution(self):
        """Phân tích phân bố city_id trong missing records"""
        logger.info(f"\n🏙️  PHÂN TÍCH PHÂN BỐ CITY_ID")
        logger.info("=" * 60)
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            cursor.execute("""
                SELECT city_id, COUNT(*) as count
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                GROUP BY city_id
                ORDER BY count DESC
                LIMIT 10
            """)
            city_distribution = cursor.fetchall()
            
            logger.info("Top 10 city_id trong missing records:")
            headers = ["City_ID", "Số lượng", "% trong missing"]
            data = []
            for city in city_distribution:
                percentage = city['count'] / self.missing_records * 100
                data.append([city['city_id'], city['count'], f"{percentage:.1f}%"])
            
            print(tabulate(data, headers=headers, tablefmt="grid"))
            
        except Exception as e:
            logger.error(f"❌ Lỗi phân tích city_id: {e}")
    
    def generate_recommendations(self, stats):
        """Tạo đề xuất giải pháp"""
        logger.info(f"\n💡 ĐỀ XUẤT GIẢI PHÁP")
        logger.info("=" * 60)
        
        total_missing = sum(stats.values())
        
        recommendations = [
            {
                'issue': 'Không có tọa độ (NULL)',
                'count': stats['null_coords'],
                'percentage': stats['null_coords']/total_missing*100,
                'solutions': [
                    'Geocoding từ address_old nếu có',
                    'Manual data entry cho các địa điểm quan trọng',
                    'Sử dụng Google Maps API để tìm tọa độ'
                ]
            },
            {
                'issue': 'Tọa độ = 0',
                'count': stats['zero_coords'],
                'percentage': stats['zero_coords']/total_missing*100,
                'solutions': [
                    'Tương tự như NULL coordinates',
                    'Kiểm tra data import process',
                    'Validation rules cho coordinate input'
                ]
            },
            {
                'issue': 'Tọa độ ngoài phạm vi VN',
                'count': stats['invalid_coords'],
                'percentage': stats['invalid_coords']/total_missing*100,
                'solutions': [
                    'Data validation và cleaning',
                    'Kiểm tra coordinate format (có thể bị swap lat/long)',
                    'Manual review cho các trường hợp đặc biệt'
                ]
            },
            {
                'issue': 'Không có address_old',
                'count': stats['no_address_old'],
                'percentage': stats['no_address_old']/total_missing*100,
                'solutions': [
                    'Reverse geocoding từ tọa độ',
                    'Sử dụng title field để tạo address',
                    'Data enrichment từ external sources'
                ]
            },
            {
                'issue': 'Geometry mismatch',
                'count': stats['geometry_mismatch'],
                'percentage': stats['geometry_mismatch']/total_missing*100,
                'solutions': [
                    'Cải thiện geometry data coverage',
                    'Sử dụng buffer zone cho point-in-polygon',
                    'Fallback to nearest ward algorithm',
                    'Manual mapping cho edge cases'
                ]
            }
        ]
        
        for rec in recommendations:
            if rec['count'] > 0:
                logger.info(f"\n🔧 {rec['issue']} ({rec['count']:,} bản ghi - {rec['percentage']:.1f}%):")
                for i, solution in enumerate(rec['solutions'], 1):
                    logger.info(f"  {i}. {solution}")
    
    def run_analysis(self):
        """Chạy phân tích toàn diện"""
        logger.info("🔍 BẮT ĐẦU PHÂN TÍCH MISSING RECORDS")
        logger.info("=" * 60)
        
        # Kết nối database
        self.connection = self.get_database_connection()
        if not self.connection:
            return
        
        try:
            # Thống kê cơ bản
            self.get_basic_statistics()
            
            # Phân tích vấn đề tọa độ
            stats = self.analyze_coordinate_issues()
            
            # Lấy mẫu dữ liệu
            self.get_samples_for_each_issue(stats)
            
            # Phân tích city_id
            self.analyze_city_id_distribution()
            
            # Đề xuất giải pháp
            self.generate_recommendations(stats)
            
            logger.info(f"\n✅ HOÀN THÀNH PHÂN TÍCH")
            
        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình phân tích: {e}")
        finally:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("🔌 Đã đóng kết nối database")

def main():
    """Hàm main"""
    analyzer = MissingRecordsAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()

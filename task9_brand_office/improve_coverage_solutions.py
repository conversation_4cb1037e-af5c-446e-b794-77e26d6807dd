#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script cải thiện coverage cho Task 9 - Implement các gi<PERSON>i pháp đề xuất
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, shape
import json
import logging
import time

# Thi<PERSON><PERSON> lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CoverageImprover:
    def __init__(self):
        self.connection = None
        self.geo_ward_data = []
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def load_geo_ward_data(self):
        """Load dữ liệu geometry từ geo_ward"""
        logger.info("🗺️  Loading geo_ward data...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            cursor.execute("""
                SELECT id, geo_province_code, province_title, ward_title, code, geometry
                FROM geo_ward 
                WHERE geometry IS NOT NULL AND geometry != ''
            """)
            self.geo_ward_data = cursor.fetchall()
            logger.info(f"📊 Đã load {len(self.geo_ward_data)} ward geometries")
            
        except Exception as e:
            logger.error(f"❌ Lỗi load geo_ward: {e}")
    
    def parse_geometry(self, geometry_json):
        """Parse geometry JSON thành Shapely geometry"""
        try:
            geometry_data = json.loads(geometry_json)
            if 'geometry' in geometry_data:
                geom_info = geometry_data['geometry']
                from shapely.geometry import shape
                geometry = shape(geom_info)
                return geometry
            return None
        except Exception as e:
            return None
    
    def find_ward_with_buffer(self, latitude, longitude, buffer_size=0.001):
        """Tìm ward với buffer zone (default ~100m)"""
        point = Point(longitude, latitude)
        
        for ward in self.geo_ward_data:
            try:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry:
                    # Thử exact match trước
                    if geometry.contains(point):
                        return ward, 'exact'
                    
                    # Thử với buffer
                    buffered_geometry = geometry.buffer(buffer_size)
                    if buffered_geometry.contains(point):
                        return ward, 'buffered'
            except Exception as e:
                continue
        
        return None, None
    
    def find_nearest_ward(self, latitude, longitude, max_distance=0.05):
        """Tìm ward gần nhất (max_distance ~5km)"""
        point = Point(longitude, latitude)
        min_distance = float('inf')
        nearest_ward = None
        
        for ward in self.geo_ward_data:
            try:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry:
                    # Tính khoảng cách đến centroid của ward
                    distance = point.distance(geometry.centroid)
                    if distance < min_distance and distance <= max_distance:
                        min_distance = distance
                        nearest_ward = ward
            except Exception as e:
                continue
        
        return nearest_ward, min_distance if nearest_ward else None
    
    def process_geometry_mismatch_records(self, limit=100):
        """Xử lý các bản ghi geometry mismatch với buffer và nearest ward"""
        logger.info(f"🔧 Xử lý geometry mismatch records (limit: {limit})...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy các bản ghi geometry mismatch
            cursor.execute("""
                SELECT id, city_id, title, address_old, latitude, longitude
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND latitude BETWEEN 8.0 AND 24.0 
                  AND longitude BETWEEN 102.0 AND 110.0
                  AND address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''
                LIMIT %s
            """, (limit,))
            
            records = cursor.fetchall()
            logger.info(f"📊 Đang xử lý {len(records)} bản ghi geometry mismatch")
            
            improved_records = []
            
            for i, record in enumerate(records):
                if i % 10 == 0:
                    logger.info(f"  Đã xử lý {i}/{len(records)} bản ghi...")
                
                # Thử buffer zone
                ward, match_type = self.find_ward_with_buffer(
                    record['latitude'], 
                    record['longitude']
                )
                
                if not ward:
                    # Thử nearest ward
                    ward, distance = self.find_nearest_ward(
                        record['latitude'], 
                        record['longitude']
                    )
                    match_type = f'nearest_{distance:.4f}' if ward else None
                
                if ward:
                    # Tạo address mới
                    address_prefix = self.extract_address_parts(record['address_old'])
                    if address_prefix:
                        new_address = f"{address_prefix}, {ward['ward_title']}, {ward['province_title']}"
                    else:
                        new_address = f"{ward['ward_title']}, {ward['province_title']}"
                    
                    improved_records.append({
                        'brand_office_id': record['id'],
                        'old_address': record['address_old'],
                        'new_address': new_address,
                        'ward_title': ward['ward_title'],
                        'province_title': ward['province_title'],
                        'match_type': match_type,
                        'coordinates': f"({record['latitude']}, {record['longitude']})"
                    })
            
            logger.info(f"✅ Đã cải thiện {len(improved_records)} bản ghi")
            return improved_records
            
        except Exception as e:
            logger.error(f"❌ Lỗi xử lý geometry mismatch: {e}")
            return []
    
    def extract_address_parts(self, address_old):
        """Trích xuất phần đầu của địa chỉ"""
        if not address_old:
            return ""
        
        # Split theo dấu phẩy
        parts = [part.strip() for part in address_old.split(',')]
        
        # Thường thì 2 phần đầu là số nhà và tên đường
        if len(parts) >= 2:
            return f"{parts[0]}, {parts[1]}"
        elif len(parts) == 1:
            return parts[0]
        else:
            return ""
    
    def process_city_id_zero_records(self, limit=100):
        """Xử lý đặc biệt các bản ghi có city_id=0 với tọa độ hợp lệ"""
        logger.info(f"🆔 Xử lý bản ghi city_id=0 (limit: {limit})...")

        try:
            cursor = self.connection.cursor(dictionary=True)

            # Lấy các bản ghi có city_id=0 nhưng tọa độ hợp lệ
            cursor.execute("""
                SELECT id, city_id, title, address_old, latitude, longitude
                FROM brand_office
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND city_id = 0
                  AND latitude IS NOT NULL AND latitude != 0
                  AND longitude IS NOT NULL AND longitude != 0
                  AND latitude BETWEEN 8.0 AND 24.0
                  AND longitude BETWEEN 102.0 AND 110.0
                  AND address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''
                LIMIT %s
            """, (limit,))

            records = cursor.fetchall()
            logger.info(f"📊 Đang xử lý {len(records)} bản ghi city_id=0 với tọa độ hợp lệ")

            improved_records = []

            for i, record in enumerate(records):
                if i % 10 == 0:
                    logger.info(f"  Đã xử lý {i}/{len(records)} bản ghi city_id=0...")

                # Tìm kiếm toàn bộ geometry data (không filter theo province)
                ward, match_type = self.find_ward_with_buffer_global(
                    record['latitude'],
                    record['longitude']
                )

                if not ward:
                    # Thử nearest ward toàn cục
                    ward, distance = self.find_nearest_ward_global(
                        record['latitude'],
                        record['longitude']
                    )
                    match_type = f'nearest_global_{distance:.4f}' if ward else None

                if ward:
                    # Tạo address mới
                    address_prefix = self.extract_address_parts(record['address_old'])
                    if address_prefix:
                        new_address = f"{address_prefix}, {ward['ward_title']}, {ward['province_title']}"
                    else:
                        new_address = f"{ward['ward_title']}, {ward['province_title']}"

                    improved_records.append({
                        'brand_office_id': record['id'],
                        'old_city_id': record['city_id'],
                        'old_address': record['address_old'],
                        'new_address': new_address,
                        'ward_title': ward['ward_title'],
                        'province_title': ward['province_title'],
                        'ward_code': ward['code'],
                        'geo_province_code': ward['geo_province_code'],
                        'match_type': match_type,
                        'coordinates': f"({record['latitude']}, {record['longitude']})"
                    })

            logger.info(f"✅ Đã cải thiện {len(improved_records)} bản ghi city_id=0")
            return improved_records

        except Exception as e:
            logger.error(f"❌ Lỗi xử lý city_id=0: {e}")
            return []

    def find_ward_with_buffer_global(self, latitude, longitude, buffer_size=0.001):
        """Tìm ward toàn cục với buffer zone (không filter theo province)"""
        point = Point(longitude, latitude)

        for ward in self.geo_ward_data:
            try:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry:
                    # Thử exact match trước
                    if geometry.contains(point):
                        return ward, 'exact_global'

                    # Thử với buffer
                    buffered_geometry = geometry.buffer(buffer_size)
                    if buffered_geometry.contains(point):
                        return ward, 'buffered_global'
            except Exception as e:
                continue

        return None, None

    def find_nearest_ward_global(self, latitude, longitude, max_distance=0.05):
        """Tìm ward gần nhất toàn cục (không filter theo province)"""
        point = Point(longitude, latitude)
        min_distance = float('inf')
        nearest_ward = None

        for ward in self.geo_ward_data:
            try:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry:
                    # Tính khoảng cách đến centroid của ward
                    distance = point.distance(geometry.centroid)
                    if distance < min_distance and distance <= max_distance:
                        min_distance = distance
                        nearest_ward = ward
            except Exception as e:
                continue

        return nearest_ward, min_distance if nearest_ward else None

    def analyze_invalid_coordinates(self, limit=50):
        """Phân tích và đề xuất fix cho tọa độ invalid"""
        logger.info(f"🔍 Phân tích tọa độ invalid (limit: {limit})...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy các bản ghi có tọa độ ngoài phạm vi VN
            cursor.execute("""
                SELECT id, city_id, title, address_old, latitude, longitude
                FROM brand_office 
                WHERE (address IS NULL OR address = '' OR TRIM(address) = '')
                  AND latitude IS NOT NULL AND longitude IS NOT NULL
                  AND latitude != 0 AND longitude != 0
                  AND (latitude NOT BETWEEN 8.0 AND 24.0 OR longitude NOT BETWEEN 102.0 AND 110.0)
                LIMIT %s
            """, (limit,))
            
            records = cursor.fetchall()
            logger.info(f"📊 Phân tích {len(records)} bản ghi tọa độ invalid")
            
            analysis_results = []
            
            for record in records:
                lat, lng = record['latitude'], record['longitude']
                address_old = record['address_old']
                
                # Phân tích pattern
                analysis = {
                    'brand_office_id': record['id'],
                    'current_coords': f"({lat}, {lng})",
                    'address_old': address_old,
                    'issues': [],
                    'suggestions': []
                }
                
                # Kiểm tra các pattern phổ biến
                if lat == 1.0 and lng == 10.0:
                    analysis['issues'].append('Placeholder coordinates (1.0, 10.0)')
                    analysis['suggestions'].append('Geocode from address_old')
                
                if lat < 8.0 or lat > 24.0:
                    analysis['issues'].append(f'Latitude {lat} outside Vietnam range (8.0-24.0)')
                    if 102.0 <= lng <= 110.0:
                        analysis['suggestions'].append('Possible lat/lng swap')
                
                if lng < 102.0 or lng > 110.0:
                    analysis['issues'].append(f'Longitude {lng} outside Vietnam range (102.0-110.0)')
                    if 8.0 <= lat <= 24.0:
                        analysis['suggestions'].append('Possible lat/lng swap')
                
                # Kiểm tra address có URL không
                if address_old and address_old.startswith('http'):
                    analysis['issues'].append('Address contains URL')
                    analysis['suggestions'].append('Extract location from URL or manual entry')
                
                analysis_results.append(analysis)
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"❌ Lỗi phân tích invalid coordinates: {e}")
            return []
    
    def generate_improvement_report(self, improved_records, city_id_zero_records, invalid_analysis):
        """Tạo báo cáo cải thiện"""
        logger.info(f"\n📊 BÁO CÁO CẢI THIỆN COVERAGE")
        logger.info("=" * 60)

        # Thống kê cải thiện geometry mismatch
        if improved_records:
            logger.info(f"✅ Geometry Mismatch Improvements: {len(improved_records)} bản ghi")

            # Thống kê theo match type
            match_types = {}
            for record in improved_records:
                match_type = record['match_type']
                if match_type not in match_types:
                    match_types[match_type] = 0
                match_types[match_type] += 1

            logger.info("📈 Phân bố theo phương pháp match:")
            for match_type, count in match_types.items():
                logger.info(f"  - {match_type}: {count} bản ghi")

            # Mẫu kết quả
            logger.info(f"\n📋 Mẫu 3 kết quả cải thiện:")
            for i, record in enumerate(improved_records[:3], 1):
                logger.info(f"  {i}. ID {record['brand_office_id']} ({record['match_type']}):")
                logger.info(f"     Old: {record['old_address'][:50]}...")
                logger.info(f"     New: {record['new_address'][:50]}...")

        # Thống kê cải thiện city_id=0
        if city_id_zero_records:
            logger.info(f"\n🆔 City_ID=0 Improvements: {len(city_id_zero_records)} bản ghi")

            # Thống kê theo match type
            match_types_zero = {}
            for record in city_id_zero_records:
                match_type = record['match_type']
                if match_type not in match_types_zero:
                    match_types_zero[match_type] = 0
                match_types_zero[match_type] += 1

            logger.info("📈 Phân bố theo phương pháp match (city_id=0):")
            for match_type, count in match_types_zero.items():
                logger.info(f"  - {match_type}: {count} bản ghi")

            # Thống kê theo tỉnh được tìm thấy
            province_counts = {}
            for record in city_id_zero_records:
                province = record['province_title']
                if province not in province_counts:
                    province_counts[province] = 0
                province_counts[province] += 1

            logger.info("📍 Phân bố theo tỉnh được tìm thấy:")
            for province, count in sorted(province_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"  - {province}: {count} bản ghi")

            # Mẫu kết quả city_id=0
            logger.info(f"\n📋 Mẫu 3 kết quả cải thiện city_id=0:")
            for i, record in enumerate(city_id_zero_records[:3], 1):
                logger.info(f"  {i}. ID {record['brand_office_id']} ({record['match_type']}):")
                logger.info(f"     Old city_id: {record['old_city_id']}")
                logger.info(f"     Found province: {record['province_title']}")
                logger.info(f"     Old: {record['old_address'][:50]}...")
                logger.info(f"     New: {record['new_address'][:50]}...")

        # Thống kê invalid coordinates
        if invalid_analysis:
            logger.info(f"\n🔍 Invalid Coordinates Analysis: {len(invalid_analysis)} bản ghi")

            # Thống kê issues
            issue_counts = {}
            for analysis in invalid_analysis:
                for issue in analysis['issues']:
                    if issue not in issue_counts:
                        issue_counts[issue] = 0
                    issue_counts[issue] += 1

            logger.info("📈 Phân bố theo loại lỗi:")
            for issue, count in issue_counts.items():
                logger.info(f"  - {issue}: {count} bản ghi")
    
    def save_results_to_csv(self, improved_records, city_id_zero_records, invalid_analysis):
        """Lưu kết quả vào CSV"""
        import csv
        import os
        from datetime import datetime

        os.makedirs('exports', exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Lưu improved records (geometry mismatch)
        if improved_records:
            filename = f"exports/improved_geometry_mismatch_{timestamp}.csv"
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = improved_records[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(improved_records)

            logger.info(f"💾 Đã lưu improved records vào {filename}")

        # Lưu city_id=0 improved records
        if city_id_zero_records:
            filename_zero = f"exports/city_id_zero_improved_{timestamp}.csv"
            with open(filename_zero, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = city_id_zero_records[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(city_id_zero_records)

            logger.info(f"💾 Đã lưu city_id=0 improved records vào {filename_zero}")

        # Lưu invalid analysis
        if invalid_analysis:
            filename2 = f"exports/invalid_coordinates_analysis_{timestamp}.csv"
            with open(filename2, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['brand_office_id', 'current_coords', 'address_old', 'issues', 'suggestions']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for analysis in invalid_analysis:
                    row = analysis.copy()
                    row['issues'] = '; '.join(analysis['issues'])
                    row['suggestions'] = '; '.join(analysis['suggestions'])
                    writer.writerow(row)

            logger.info(f"💾 Đã lưu invalid analysis vào {filename2}")
    
    def run_improvement(self, geometry_limit=100, city_id_zero_limit=100, invalid_limit=50):
        """Chạy quá trình cải thiện coverage"""
        logger.info("🚀 BẮT ĐẦU CẢI THIỆN COVERAGE")
        logger.info("=" * 60)

        # Kết nối database
        self.connection = self.get_database_connection()
        if not self.connection:
            return

        try:
            # Load geo_ward data
            self.load_geo_ward_data()

            # Xử lý geometry mismatch
            improved_records = self.process_geometry_mismatch_records(geometry_limit)

            # Xử lý city_id=0 records
            city_id_zero_records = self.process_city_id_zero_records(city_id_zero_limit)

            # Phân tích invalid coordinates
            invalid_analysis = self.analyze_invalid_coordinates(invalid_limit)

            # Tạo báo cáo
            self.generate_improvement_report(improved_records, city_id_zero_records, invalid_analysis)

            # Lưu kết quả
            self.save_results_to_csv(improved_records, city_id_zero_records, invalid_analysis)

            # Tổng kết
            total_improved = len(improved_records) + len(city_id_zero_records)
            logger.info(f"\n🎯 TỔNG KẾT CẢI THIỆN:")
            logger.info(f"  - Geometry mismatch: {len(improved_records)} bản ghi")
            logger.info(f"  - City_ID=0: {len(city_id_zero_records)} bản ghi")
            logger.info(f"  - Tổng cộng: {total_improved} bản ghi được cải thiện")

            logger.info(f"\n✅ HOÀN THÀNH CẢI THIỆN COVERAGE")

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình cải thiện: {e}")
        finally:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("🔌 Đã đóng kết nối database")

def main():
    """Hàm main"""
    import sys

    geometry_limit = 100
    city_id_zero_limit = 100
    invalid_limit = 50

    if len(sys.argv) > 1:
        geometry_limit = int(sys.argv[1])
    if len(sys.argv) > 2:
        city_id_zero_limit = int(sys.argv[2])
    if len(sys.argv) > 3:
        invalid_limit = int(sys.argv[3])

    print(f"🔧 Chạy với limits: geometry={geometry_limit}, city_id_zero={city_id_zero_limit}, invalid={invalid_limit}")

    improver = CoverageImprover()
    improver.run_improvement(geometry_limit, city_id_zero_limit, invalid_limit)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Task 9: Cập nhật địa chỉ brand_office dựa trên geometry data
Sử dụng tọa độ để xác định xã/phường và tỉnh/thành phố từ geo_ward
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point
import json
import csv
import time
from datetime import datetime
import logging
import os

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_brand_office.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BrandOfficeUpdater:
    def __init__(self):
        self.connection = None
        self.results = []
        self.batch_size = 1000
        
    def get_database_connection(self):
        """Tạo kết nối database"""
        try:
            connection = mysql.connector.connect(
                host='127.0.0.1',
                port=3306,
                database='urbox',
                user='root',
                password='root'
            )
            
            if connection.is_connected():
                logger.info("✅ Kết nối database thành công!")
                return connection
                
        except Error as e:
            logger.error(f"❌ Lỗi kết nối database: {e}")
            return None
    
    def get_brand_office_data(self, limit=None):
        """Lấy dữ liệu brand_office có tọa độ hợp lệ"""
        logger.info("🏢 Lấy dữ liệu brand_office...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            query = """
                SELECT id, city_id, title, address_old, latitude, longitude
                FROM brand_office 
                WHERE latitude BETWEEN 8.0 AND 24.0 
                  AND longitude BETWEEN 102.0 AND 110.0
                  AND address_old IS NOT NULL AND address_old != '' AND TRIM(address_old) != ''
                ORDER BY id
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query)
            records = cursor.fetchall()
            
            logger.info(f"📊 Đã lấy được {len(records)} bản ghi brand_office")
            return records
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu brand_office: {e}")
            return []
    
    def get_province_mapping(self):
        """Lấy mapping giữa city_id và new_pti_id"""
        logger.info("🗺️  Lấy mapping province...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Lấy mapping từ tỉnh cũ sang tỉnh mới
            cursor.execute("""
                SELECT old_p.id as old_id, old_p.pti_id as old_pti_id, old_p.title as old_title,
                       new_p.pti_id as new_pti_id, new_p.title as new_title
                FROM ___province old_p
                LEFT JOIN ___province new_p ON old_p.new_pti_id = new_p.pti_id AND new_p.is_new = 2
                WHERE old_p.is_new = 1
            """)
            mappings = cursor.fetchall()
            
            # Tạo dict để tra cứu nhanh
            mapping_dict = {}
            for mapping in mappings:
                mapping_dict[mapping['old_id']] = {
                    'old_pti_id': mapping['old_pti_id'],
                    'old_title': mapping['old_title'],
                    'new_pti_id': mapping['new_pti_id'],
                    'new_title': mapping['new_title']
                }
            
            logger.info(f"📊 Đã tạo mapping cho {len(mapping_dict)} tỉnh")
            return mapping_dict
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy mapping province: {e}")
            return {}
    
    def get_geo_ward_data(self):
        """Lấy dữ liệu geometry từ geo_ward"""
        logger.info("🗺️  Lấy dữ liệu geo_ward...")
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            cursor.execute("""
                SELECT id, geo_province_code, province_title, ward_title, code, geometry
                FROM geo_ward 
                WHERE geometry IS NOT NULL AND geometry != ''
                ORDER BY geo_province_code, id
            """)
            records = cursor.fetchall()
            
            logger.info(f"📊 Đã lấy được {len(records)} bản ghi geo_ward")
            return records
            
        except Error as e:
            logger.error(f"❌ Lỗi lấy dữ liệu geo_ward: {e}")
            return []
    
    def parse_geometry(self, geometry_json):
        """Parse geometry JSON thành Shapely geometry"""
        try:
            geometry_data = json.loads(geometry_json)

            if 'geometry' in geometry_data:
                geom_info = geometry_data['geometry']

                # Sử dụng shapely.geometry để tạo geometry object
                from shapely.geometry import shape
                geometry = shape(geom_info)
                return geometry

            return None

        except Exception as e:
            logger.warning(f"⚠️  Lỗi parse geometry: {str(e)[:100]}...")
            return None
    
    def extract_address_parts(self, address_old):
        """Trích xuất phần đầu của địa chỉ (số nhà, tên đường)"""
        if not address_old:
            return ""
        
        # Split theo dấu phẩy
        parts = [part.strip() for part in address_old.split(',')]
        
        # Thường thì 2 phần đầu là số nhà và tên đường
        if len(parts) >= 2:
            return f"{parts[0]}, {parts[1]}"
        elif len(parts) == 1:
            return parts[0]
        else:
            return ""
    
    def find_ward_for_point(self, latitude, longitude, geo_ward_data, province_code=None):
        """Tìm xã/phường chứa điểm tọa độ"""
        point = Point(longitude, latitude)
        
        # Filter theo province_code nếu có
        filtered_wards = geo_ward_data
        if province_code:
            filtered_wards = [w for w in geo_ward_data if w['geo_province_code'] == province_code]
        
        for ward in filtered_wards:
            try:
                geometry = self.parse_geometry(ward['geometry'])
                if geometry and geometry.contains(point):
                    return ward
            except Exception as e:
                continue
        
        return None
    
    def process_batch(self, brand_office_batch, province_mapping, geo_ward_data):
        """Xử lý một batch brand_office records"""
        batch_results = []
        
        for record in brand_office_batch:
            try:
                # Lấy thông tin mapping province
                city_id = record['city_id']
                province_info = province_mapping.get(city_id, {})
                new_pti_id = province_info.get('new_pti_id')

                # Tìm ward chứa tọa độ (không filter theo province nếu không có mapping)
                ward_info = self.find_ward_for_point(
                    record['latitude'],
                    record['longitude'],
                    geo_ward_data,
                    str(new_pti_id).zfill(2) if new_pti_id else None
                )
                
                # Trích xuất phần đầu địa chỉ
                address_prefix = self.extract_address_parts(record['address_old'])
                
                # Tạo địa chỉ mới
                new_address = ""
                if ward_info:
                    if address_prefix:
                        new_address = f"{address_prefix}, {ward_info['ward_title']}, {ward_info['province_title']}"
                    else:
                        new_address = f"{ward_info['ward_title']}, {ward_info['province_title']}"
                
                # Lưu kết quả
                result = {
                    'brand_office_id': record['id'],
                    'city_id': record['city_id'],
                    'title': record['title'],
                    'address_old': record['address_old'],
                    'latitude': record['latitude'],
                    'longitude': record['longitude'],
                    'province_code_old': province_info.get('old_pti_id'),
                    'province_title_old': province_info.get('old_title'),
                    'province_code_new': new_pti_id,
                    'province_title_new': province_info.get('new_title'),
                    'ward_found': ward_info is not None,
                    'ward_code': ward_info['code'] if ward_info else None,
                    'ward_title': ward_info['ward_title'] if ward_info else None,
                    'ward_province_title': ward_info['province_title'] if ward_info else None,
                    'address_prefix': address_prefix,
                    'new_address': new_address,
                    'processed_at': datetime.now().isoformat()
                }
                
                batch_results.append(result)
                
                if len(batch_results) % 100 == 0:
                    logger.info(f"  Đã xử lý {len(batch_results)} bản ghi trong batch")
                
            except Exception as e:
                logger.error(f"❌ Lỗi xử lý record ID {record['id']}: {e}")
                continue
        
        return batch_results

    def save_results_to_csv(self, results, filename):
        """Lưu kết quả vào file CSV"""
        logger.info(f"💾 Lưu kết quả vào {filename}...")

        if not results:
            logger.warning("⚠️  Không có kết quả để lưu")
            return

        # Tạo thư mục exports nếu chưa có
        os.makedirs('exports', exist_ok=True)

        filepath = os.path.join('exports', filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                writer.writerows(results)

            logger.info(f"✅ Đã lưu {len(results)} bản ghi vào {filepath}")

        except Exception as e:
            logger.error(f"❌ Lỗi lưu file CSV: {e}")

    def generate_summary_report(self, results):
        """Tạo báo cáo tổng kết"""
        if not results:
            return

        total_records = len(results)
        found_ward = len([r for r in results if r['ward_found']])
        not_found_ward = total_records - found_ward

        logger.info(f"\n📊 BÁO CÁO TỔNG KẾT:")
        logger.info(f"=" * 50)
        logger.info(f"📈 Tổng số bản ghi xử lý: {total_records}")
        logger.info(f"✅ Tìm được ward: {found_ward} ({found_ward/total_records*100:.1f}%)")
        logger.info(f"❌ Không tìm được ward: {not_found_ward} ({not_found_ward/total_records*100:.1f}%)")

        # Thống kê theo tỉnh
        province_stats = {}
        for result in results:
            province = result['province_title_new'] or 'Unknown'
            if province not in province_stats:
                province_stats[province] = {'total': 0, 'found': 0}
            province_stats[province]['total'] += 1
            if result['ward_found']:
                province_stats[province]['found'] += 1

        logger.info(f"\n📊 Thống kê theo tỉnh:")
        for province, stats in sorted(province_stats.items()):
            success_rate = stats['found']/stats['total']*100 if stats['total'] > 0 else 0
            logger.info(f"  {province}: {stats['found']}/{stats['total']} ({success_rate:.1f}%)")

    def run(self, limit=None):
        """Chạy quá trình cập nhật"""
        logger.info("🚀 BẮT ĐẦU TASK 9: CẬP NHẬT ĐỊA CHỈ brand_office")
        logger.info("=" * 60)

        # Kết nối database
        self.connection = self.get_database_connection()
        if not self.connection:
            return

        try:
            # Lấy dữ liệu
            logger.info("📥 Bước 1: Lấy dữ liệu từ database...")
            brand_office_data = self.get_brand_office_data(limit)
            province_mapping = self.get_province_mapping()
            geo_ward_data = self.get_geo_ward_data()

            if not brand_office_data:
                logger.error("❌ Không có dữ liệu brand_office để xử lý")
                return

            if not geo_ward_data:
                logger.error("❌ Không có dữ liệu geo_ward để xử lý")
                return

            # Xử lý theo batch
            logger.info(f"⚙️  Bước 2: Xử lý {len(brand_office_data)} bản ghi...")
            all_results = []

            for i in range(0, len(brand_office_data), self.batch_size):
                batch = brand_office_data[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (len(brand_office_data) + self.batch_size - 1) // self.batch_size

                logger.info(f"🔄 Xử lý batch {batch_num}/{total_batches} ({len(batch)} bản ghi)...")

                batch_results = self.process_batch(batch, province_mapping, geo_ward_data)
                all_results.extend(batch_results)

                # Nghỉ giữa các batch
                if i + self.batch_size < len(brand_office_data):
                    time.sleep(1)

            # Lưu kết quả
            logger.info("💾 Bước 3: Lưu kết quả...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"brand_office_address_update_{timestamp}.csv"
            self.save_results_to_csv(all_results, filename)

            # Tạo báo cáo
            self.generate_summary_report(all_results)

            logger.info("✅ HOÀN THÀNH TASK 9!")

        except Exception as e:
            logger.error(f"❌ Lỗi trong quá trình xử lý: {e}")
        finally:
            if self.connection and self.connection.is_connected():
                self.connection.close()
                logger.info("🔌 Đã đóng kết nối database")

def main():
    """Hàm main"""
    import sys

    updater = BrandOfficeUpdater()

    # Kiểm tra argument
    if len(sys.argv) > 1:
        if sys.argv[1] == "full":
            print("🚀 Chạy TOÀN BỘ dữ liệu brand_office...")
            updater.run()
        elif sys.argv[1].isdigit():
            limit = int(sys.argv[1])
            print(f"🧪 Chạy test với {limit} bản ghi đầu tiên...")
            updater.run(limit=limit)
        else:
            print("Usage: python update_brand_office_address.py [full|number]")
    else:
        # Mặc định chạy với 100 bản ghi
        print("🧪 Chạy test với 100 bản ghi đầu tiên...")
        print("💡 Sử dụng: python update_brand_office_address.py full (để chạy toàn bộ)")
        print("💡 Hoặc: python update_brand_office_address.py 1000 (để chạy 1000 bản ghi)")
        updater.run(limit=100)

if __name__ == "__main__":
    main()

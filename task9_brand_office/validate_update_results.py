#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script validation kết quả cập nhật database brand_office
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
from tabulate import tabulate
import logging

# Thiết lập logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_database_connection():
    """Tạo kết nối database"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            logger.info("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        logger.error(f"❌ Lỗi kết nối database: {e}")
        return None

def validate_update_results():
    """Validation kết quả cập nhật"""
    logger.info("🔍 BẮT ĐẦU VALIDATION KẾT QUẢ CẬP NHẬT")
    logger.info("=" * 60)
    
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # 1. Kiểm tra tổng số bản ghi có address mới
        cursor.execute("""
            SELECT COUNT(*) as total_with_address
            FROM brand_office 
            WHERE address IS NOT NULL AND address != '' AND TRIM(address) != ''
        """)
        total_with_address = cursor.fetchone()['total_with_address']
        logger.info(f"📊 Tổng số bản ghi có address: {total_with_address}")
        
        # 2. Kiểm tra format địa chỉ mới
        cursor.execute("""
            SELECT COUNT(*) as count_new_format
            FROM brand_office 
            WHERE address LIKE '%Phường %' OR address LIKE '%Xã %' OR address LIKE '%Thị trấn %'
        """)
        new_format_count = cursor.fetchone()['count_new_format']
        logger.info(f"📝 Số bản ghi có format địa chỉ mới: {new_format_count}")
        
        # 3. Lấy mẫu kết quả
        cursor.execute("""
            SELECT id, address, address_old, latitude, longitude
            FROM brand_office 
            WHERE address IS NOT NULL AND address != ''
            ORDER BY id
            LIMIT 10
        """)
        samples = cursor.fetchall()
        
        logger.info(f"\n📋 Mẫu 10 bản ghi sau cập nhật:")
        if samples:
            headers = ["ID", "Address Mới", "Address Cũ", "Lat", "Long"]
            data = []
            for s in samples:
                address_new = (s['address'][:50] + '...') if s['address'] and len(s['address']) > 50 else s['address']
                address_old = (s['address_old'][:50] + '...') if s['address_old'] and len(s['address_old']) > 50 else s['address_old']
                data.append([
                    s['id'], 
                    address_new,
                    address_old,
                    s['latitude'], 
                    s['longitude']
                ])
            print(tabulate(data, headers=headers, tablefmt="grid"))
        
        # 4. So sánh trước/sau
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN address IS NOT NULL AND address != '' THEN 1 END) as with_new_address,
                COUNT(CASE WHEN address_old IS NOT NULL AND address_old != '' THEN 1 END) as with_old_address
            FROM brand_office
        """)
        comparison = cursor.fetchone()
        
        logger.info(f"\n📊 So sánh trước/sau cập nhật:")
        logger.info(f"  - Tổng số bản ghi: {comparison['total']}")
        logger.info(f"  - Có address mới: {comparison['with_new_address']}")
        logger.info(f"  - Có address_old: {comparison['with_old_address']}")
        
        # 5. Kiểm tra các trường hợp đặc biệt
        cursor.execute("""
            SELECT COUNT(*) as count_both_null
            FROM brand_office 
            WHERE (address IS NULL OR address = '') 
              AND (address_old IS NULL OR address_old = '')
        """)
        both_null = cursor.fetchone()['count_both_null']
        logger.info(f"⚠️  Bản ghi không có cả address và address_old: {both_null}")
        
        # 6. Kiểm tra format địa chỉ cụ thể
        cursor.execute("""
            SELECT address, COUNT(*) as count
            FROM brand_office 
            WHERE address IS NOT NULL AND address != ''
            GROUP BY address
            ORDER BY count DESC
            LIMIT 5
        """)
        top_addresses = cursor.fetchall()
        
        logger.info(f"\n📍 Top 5 địa chỉ phổ biến nhất:")
        for addr in top_addresses:
            logger.info(f"  - {addr['address'][:80]}... ({addr['count']} lần)")
        
        # 7. Validation format mới
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN address LIKE '%Thành phố %' THEN 1 END) as thanh_pho,
                COUNT(CASE WHEN address LIKE '%Tỉnh %' THEN 1 END) as tinh,
                COUNT(CASE WHEN address LIKE '%Phường %' THEN 1 END) as phuong,
                COUNT(CASE WHEN address LIKE '%Xã %' THEN 1 END) as xa,
                COUNT(CASE WHEN address LIKE '%Thị trấn %' THEN 1 END) as thi_tran
            FROM brand_office 
            WHERE address IS NOT NULL AND address != ''
        """)
        format_stats = cursor.fetchone()
        
        logger.info(f"\n📈 Thống kê format địa chỉ mới:")
        logger.info(f"  - Chứa 'Thành phố': {format_stats['thanh_pho']}")
        logger.info(f"  - Chứa 'Tỉnh': {format_stats['tinh']}")
        logger.info(f"  - Chứa 'Phường': {format_stats['phuong']}")
        logger.info(f"  - Chứa 'Xã': {format_stats['xa']}")
        logger.info(f"  - Chứa 'Thị trấn': {format_stats['thi_tran']}")
        
        # 8. Tính tỷ lệ thành công
        success_rate = (total_with_address / comparison['total'] * 100) if comparison['total'] > 0 else 0
        logger.info(f"\n✅ Tỷ lệ cập nhật thành công: {success_rate:.1f}%")
        
        if success_rate >= 90:
            logger.info("🎉 CẬP NHẬT THÀNH CÔNG XUẤT SẮC!")
        elif success_rate >= 80:
            logger.info("👍 Cập nhật thành công tốt")
        else:
            logger.warning("⚠️  Cần kiểm tra lại kết quả cập nhật")
        
    except Exception as e:
        logger.error(f"❌ Lỗi validation: {e}")
    finally:
        if connection.is_connected():
            connection.close()
            logger.info("🔌 Đã đóng kết nối database")

def main():
    """Hàm main"""
    validate_update_results()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug province mapping issue
"""

import mysql.connector
from mysql.connector import Error

def get_database_connection():
    """Tạo kết nối database"""
    try:
        connection = mysql.connector.connect(
            host='127.0.0.1',
            port=3306,
            database='urbox',
            user='root',
            password='root'
        )
        
        if connection.is_connected():
            print("✅ Kết nối database thành công!")
            return connection
            
    except Error as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def debug_province_mapping():
    """Debug province mapping"""
    connection = get_database_connection()
    if not connection:
        return
    
    try:
        cursor = connection.cursor(dictionary=True)
        
        # Kiểm tra city_id 82 (từ test data)
        print("🔍 Kiểm tra city_id 82:")
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id, is_new
            FROM ___province
            WHERE id = 82
        """)
        city_82 = cursor.fetchone()
        print(f"City 82: {city_82}")

        # Kiểm tra range ID trong ___province
        cursor.execute("""
            SELECT MIN(id) as min_id, MAX(id) as max_id, COUNT(*) as total
            FROM ___province
        """)
        id_range = cursor.fetchone()
        print(f"ID range trong ___province: {id_range}")

        # Tìm city_id gần 82
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id, is_new
            FROM ___province
            WHERE id BETWEEN 80 AND 85
            ORDER BY id
        """)
        near_82 = cursor.fetchall()
        print(f"IDs gần 82: {near_82}")
        
        # Kiểm tra có new_pti_id nào không
        cursor.execute("""
            SELECT COUNT(*) as count_with_new_pti_id
            FROM ___province 
            WHERE new_pti_id IS NOT NULL AND new_pti_id != ''
        """)
        count_new_pti = cursor.fetchone()
        print(f"Số tỉnh có new_pti_id: {count_new_pti['count_with_new_pti_id']}")
        
        # Lấy mẫu tỉnh có new_pti_id
        cursor.execute("""
            SELECT id, pti_id, title, new_pti_id, is_new
            FROM ___province 
            WHERE new_pti_id IS NOT NULL AND new_pti_id != ''
            LIMIT 5
        """)
        samples = cursor.fetchall()
        print(f"\nMẫu tỉnh có new_pti_id:")
        for sample in samples:
            print(f"  {sample}")
        
        # Kiểm tra mapping logic
        print(f"\n🔍 Kiểm tra mapping logic:")
        cursor.execute("""
            SELECT old_p.id as old_id, old_p.pti_id as old_pti_id, old_p.title as old_title,
                   old_p.new_pti_id, old_p.is_new as old_is_new,
                   new_p.pti_id as new_pti_id, new_p.title as new_title, new_p.is_new as new_is_new
            FROM ___province old_p
            LEFT JOIN ___province new_p ON old_p.new_pti_id = new_p.pti_id AND new_p.is_new = 2
            WHERE old_p.is_new = 1 AND old_p.id = 82
        """)
        mapping_82 = cursor.fetchone()
        print(f"Mapping cho city_id 82: {mapping_82}")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    finally:
        if connection.is_connected():
            connection.close()

if __name__ == "__main__":
    debug_province_mapping()
